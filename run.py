import requests
import json
import time
import os
from datetime import datetime

def save_data_to_file(data, timestamp):
    """
    保存完整的API响应数据到本地文件
    """
    try:
        # 创建data目录（如果不存在）
        if not os.path.exists('data'):
            os.makedirs('data')

        # 生成文件名：data/football_scores_YYYYMMDD_HHMMSS.json
        filename = f"data/football_scores_{timestamp.strftime('%Y%m%d_%H%M%S')}.json"

        # 保存完整的JSON数据
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)

        print(f"✅ 数据已保存到: {filename}")

        # 同时保存到最新数据文件（覆盖式）
        latest_filename = "data/latest_football_scores.json"
        with open(latest_filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)

        return filename

    except Exception as e:
        print(f"❌ 保存数据失败: {e}")
        return None


def get_football_scores():
    """
    模拟应用请求，获取足球比分数据。
    """
    # 1. 定义请求的 URL 和参数
    # 基础 URL
    base_url = "https://api.dszuqiu.com/v7/score"

    # 请求参数 (Query Parameters)
    # token 是核心，但可能会过期。request_time 需要动态生成。
    params = {
        "mt": "0",
        "request_time": f"{time.time():.6f}",  # 动态生成当前时间戳
        "token": "647303-b1012479e156d073377d7494a87dfd15" # 使用您抓包获取的 token
    }

    # 2. 定义请求头 (Headers)
    # 必须完整模拟，特别是 User-Agent 和 X- 开头的自定义头部
    headers = {
        "Host": "api.dszuqiu.com",
        "X-APP-TYPE": "1",
        "X-APP-BRANCH": "1",
        "Accept-Language": "zh-Hans-TW;q=1, en-TW;q=0.9",
        "Accept-Encoding": "gzip, deflate, br",
        "Accept": "*/*",
        "X-APP-DEVICE": "0E99BA31-9AD7-4DDB-9D4F-E0B21A143BF8",
        "User-Agent": "DSZuQiu/2.2.7 (iPhone; iOS 16.2; Scale/3.00)",
        "X-APP-PUSH": "2",
        "Connection": "keep-alive",
        "X-DEVICE-ID": "2fa0d0dec9a847708f79e10c60af94bc",
        "X-APP-VERSION": "227"
    }

    print("--- 开始请求 ---")
    print(f"URL: {base_url}")
    print(f"参数: {params}")
    
    try:
        # 3. 发送 GET 请求
        response = requests.get(base_url, params=params, headers=headers, timeout=15)

        # 检查 HTTP 响应状态码是否为 200
        response.raise_for_status()
        
        print(f"\n--- 请求成功 (状态码: {response.status_code}) ---")

        # 4. 解析 JSON 响应
        data = response.json()

        # 保存完整数据到文件
        current_time = datetime.now()
        saved_file = save_data_to_file(data, current_time)

        # 5. 处理并打印数据
        if data.get("result") == "success" and "rs" in data:
            matches = data["rs"]
            print(f"成功获取到 {len(matches)} 场比赛数据。\n")
            
            # 打印前3场比赛的核心信息作为示例
            for match in matches[:3]:
                league_name = match.get("league", {}).get("n", "N/A")
                host_name = match.get("host", {}).get("n", "N/A")
                guest_name = match.get("guest", {}).get("n", "N/A")
                match_status = match.get("status", "N/A")
                
                # 如果比赛正在进行，显示比分
                if match_status not in ["未", "半"]:
                    score = f"{match.get('rd', {}).get('hg', '0')} - {match.get('rd', {}).get('gg', '0')}"
                    print(f"联赛: {league_name} | 状态: {match_status}' | 比分: {score}")
                else:
                    print(f"联赛: {league_name} | 状态: {match_status}")
                
                print(f"  主队: {host_name}")
                print(f"  客队: {guest_name}")
                print("-" * 20)

        else:
            print("响应数据格式不正确或请求未成功。")
            print(json.dumps(data, indent=2, ensure_ascii=False))

    except requests.exceptions.RequestException as e:
        print(f"\n--- 请求失败 ---")
        print(f"错误: {e}")
    except json.JSONDecodeError:
        print("\n--- 解析 JSON 失败 ---")
        print(f"无法解析响应内容: {response.text}")


def run_continuous():
    """
    每5分钟请求一次数据的主循环
    """
    print("脚本开始执行...")
    print("每5分钟自动请求一次数据，按 Ctrl+C 停止")
    print("=" * 50)

    try:
        while True:
            # 显示当前时间
            current_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
            print(f"\n[{current_time}] 开始获取数据...")

            # 获取足球比分数据
            get_football_scores()

            print(f"[{current_time}] 数据获取完成，等待5分钟后继续...")
            print("=" * 50)

            # 等待5分钟 (300秒)
            time.sleep(300)

    except KeyboardInterrupt:
        print("\n\n用户中断程序，脚本停止执行。")
    except Exception as e:
        print(f"\n程序发生异常: {e}")
        print("脚本异常退出。")


if __name__ == "__main__":
    run_continuous()