# 足球比分自动获取脚本

这个脚本可以自动从 DSZuQiu API 获取实时足球比分数据，每5分钟更新一次。

## 功能特点

- 🔄 **自动循环**：每5分钟自动请求一次数据
- 📱 **完整模拟**：模拟真实移动应用的请求头和参数
- 🕒 **实时显示**：显示当前时间和比赛状态
- 🛡️ **错误处理**：包含完善的异常处理机制
- ⏹️ **优雅停止**：支持 Ctrl+C 安全停止

## 使用方法

### 运行脚本
```bash
python3 run.py
```

### 停止脚本
按 `Ctrl+C` 停止脚本运行

## 输出示例

```
脚本开始执行...
每5分钟自动请求一次数据，按 Ctrl+C 停止
==================================================

[2025-07-22 15:39:52] 开始获取数据...
--- 开始请求 ---
URL: https://api.dszuqiu.com/v7/score
参数: {'mt': '0', 'request_time': '1753169992.144727', 'token': '647303-b1012479e156d073377d7494a87dfd15'}

--- 请求成功 (状态码: 200) ---
成功获取到 10 场比赛数据。

联赛: 欧洲友谊 | 状态: 88' | 比分: 2 - 0
  主队: 里雄莱锡安夏普尔
  客队: 荷兹利亚马卡比
--------------------
联赛: 越锦U21 | 状态: 52' | 比分: 0 - 2
  主队: 同塔 21岁以下
  客队: 河内FC 21岁以下
--------------------
[2025-07-22 15:39:52] 数据获取完成，等待5分钟后继续...
==================================================
```

## 依赖要求

- Python 3.x
- requests 库

## 注意事项

1. **Token 有效性**：脚本使用固定的 token，如果失效需要重新抓包获取
2. **网络连接**：需要稳定的网络连接
3. **请求频率**：已设置为5分钟间隔，避免过于频繁的请求
4. **长期运行**：适合长期后台运行监控比赛数据

## 文件说明

- `run.py` - 主脚本文件
- `baseContext.md` - Proxyman 抓包的原始数据
- `README.md` - 使用说明文档
