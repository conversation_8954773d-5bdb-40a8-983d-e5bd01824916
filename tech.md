技术文档
1. 接口概述
该接口 (/v7/score) 用于从 api.dszuqiu.com 服务器获取足球比赛的实时比分和相关数据。返回的数据为 JSON 格式，包含了多场比赛的详细信息。

2. 请求分析
URL
https://api.dszuqiu.com/v7/score

方法 (Method)
GET

请求参数 (Query Parameters)
这是请求中最核心的部分，直接影响请求的成败。

mt=0:

作用: 很可能是一个数据筛选参数，具体含义未知（例如，可能是按比赛类型筛选）。在我们的请求中保持为 0 即可。

request_time=1753169243.001738:

作用: 客户端发起请求时的 Unix 时间戳，精确到微秒。服务器可能用它来校验请求的时效性，防止重放攻击。

实现: 在 Python 中，我们使用 time.time() 来动态生成当前时间戳。

token=647303-b1012479e156d073377d7494a87dfd15:

作用: 这是最重要的安全令牌。它很可能是通过一个复杂的算法在 App 内部生成的，用于验证请求的合法性。

构成推测: 这个 token 可能是由 用户ID + 某个加密哈希 组成。哈希部分可能结合了 request_time、device_id 以及一个存放在 App 内部的密钥 (Secret Key) 进行 MD5 或 HMAC-SHA256 等加密运算后得到。

风险: 这个 token 是有有效期的。您抓包获取的这个值可能在几分钟或几小时后就会失效。如果失效，API 将会拒绝请求（例如返回 401 或其他错误）。要长期稳定地使用此接口，最大的挑战就是逆向分析出这个 token 的生成算法，但这通常非常困难。

请求头 (Headers)
请求头用于模拟一个真实的 App 客户端环境。

User-Agent: DSZuQiu/2.2.7 (iPhone; iOS 16.2; Scale/3.00)

明确告诉服务器，这是一个来自特定版本 DSZuQiu App 的 iPhone 客户端。必须完全一致。

X- 系列自定义头:

X-APP-TYPE, X-APP-BRANCH, X-APP-DEVICE, X-APP-PUSH, X-DEVICE-ID, X-APP-VERSION

这些是 App 自定义的头部，传递了关于 App 版本、渠道、设备ID等信息。服务器会严格校验这些值，它们很可能也参与了 token 的生成过程。因此，这些值也必须在请求中完整保留。

3. 响应分析
状态码 (Status Code)
200 OK 表示请求被服务器成功处理。

内容格式 (Content-Type)
application/json; charset=utf-8 表明响应体是 UTF-8 编码的 JSON 字符串。

内容编码 (Content-Encoding)
gzip 表示响应内容经过了 Gzip 压缩以减小传输体积。Python 的 requests 库会自动处理解压缩，我们无需关心此过程。

响应体 (Body Structure)
响应是一个 JSON 对象，其主要结构如下：

JSON

{
  "rs": [ /* 这是一个比赛列表 */ ],
  "mt": "1753198042",
  "status": 200,
  "result": "success"
}
rs: 一个数组 (list)，每个元素都是一个独立的比赛对象，包含了联赛信息、主客队、比分、状态等所有数据。

status: 200，业务层面的成功状态码。

result: "success"，业务层面的成功标识。